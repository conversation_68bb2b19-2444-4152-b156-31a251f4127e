#!/bin/bash

# Full Training Pipeline Automation Script
# This script automates the complete training pipeline:
# 1. UNet training -> 2. UNet inference -> 3. UNet evaluation
# 4. SAM2 training -> 5. SAM2 inference -> 6. SAM2 evaluation
# Supports multiple rounds of training

# Usage: ./run_full_training_pipeline.sh [CUDA_DEVICES] [NUM_ROUNDS]
# Example: ./run_full_training_pipeline.sh 0,1 3

set -e  # Exit on any error

# Default parameters
DEFAULT_CUDA_DEVICES="0"
DEFAULT_NUM_ROUNDS=1

# Parse command line arguments
CUDA_DEVICES="${1:-$DEFAULT_CUDA_DEVICES}"
NUM_ROUNDS="${2:-$DEFAULT_NUM_ROUNDS}"

# Set CUDA devices
export CUDA_VISIBLE_DEVICES="$CUDA_DEVICES"
echo "Using CUDA devices: $CUDA_VISIBLE_DEVICES"
echo "Number of training rounds: $NUM_ROUNDS"

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project root
cd "$PROJECT_ROOT"
echo "Project root: $PROJECT_ROOT"

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to run command with error handling
run_command() {
    local cmd="$1"
    local description="$2"
    
    log "Starting: $description"
    log "Command: $cmd"
    
    if eval "$cmd"; then
        log "✅ SUCCESS: $description"
        return 0
    else
        log "❌ FAILED: $description"
        echo "Command failed: $cmd"
        echo "Do you want to continue? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log "Stopping execution"
            exit 1
        fi
        return 1
    fi
}

# Function to get the latest checkpoint path
get_latest_unet_checkpoint() {
    local round_num="$1"
    local checkpoint_dir="${PROJECT_ROOT}/output/fs_unet_ckpt/mitoem_${round_num}"
    
    if [[ -f "$checkpoint_dir/last_checkpoint.pytorch" ]]; then
        echo "$checkpoint_dir/last_checkpoint.pytorch"
    elif [[ -f "$checkpoint_dir/best_checkpoint.pytorch" ]]; then
        echo "$checkpoint_dir/best_checkpoint.pytorch"
    else
        # Find the latest checkpoint file
        local latest_checkpoint=$(find "$checkpoint_dir" -name "*.pytorch" -type f 2>/dev/null | sort | tail -1)
        if [[ -n "$latest_checkpoint" ]]; then
            echo "$latest_checkpoint"
        else
            echo ""
        fi
    fi
}

# Function to get the latest SAM2 experiment directory
get_latest_sam2_experiment() {
    local sam2_ckpt_dir="${PROJECT_ROOT}/output/finetune_sam_ckpt"
    
    if [[ -d "$sam2_ckpt_dir" ]]; then
        # Find the latest experiment directory (sorted by name, assuming timestamp format)
        local latest_exp=$(find "$sam2_ckpt_dir" -maxdepth 1 -type d -name "20*" 2>/dev/null | sort | tail -1)
        if [[ -n "$latest_exp" && -f "$latest_exp/checkpoints/checkpoint.pt" ]]; then
            echo "$latest_exp"
        else
            echo ""
        fi
    else
        echo ""
    fi
}

# Main training pipeline
log "🚀 Starting full training pipeline"
log "Configuration:"
log "  - CUDA devices: $CUDA_DEVICES"
log "  - Number of rounds: $NUM_ROUNDS"
log "  - Project root: $PROJECT_ROOT"

# Initialize variables for tracking paths
PREV_UNET_PATH=""
PREV_SAM2_EXP=""

for round in $(seq 1 $NUM_ROUNDS); do
    log ""
    log "🔄 ===== ROUND $round/$NUM_ROUNDS ====="
    
    # Step 1: UNet Training
    log ""
    log "📚 Step 1: UNet Training (Round $round)"
    
    if [[ $round -eq 1 ]]; then
        # First round: use trainu_fs_mitoem config
        unet_config="trainu_fs_mitoem"
        unet_cmd="python train/train_unet.py +task=$unet_config ++task.override_unet_config.trainer.checkpoint_dir=\"\${output_root}/fs_unet_ckpt/mitoem_${round}\""
    else
        # Subsequent rounds: use trainu_fs_mitoem2 config with previous checkpoint
        unet_config="trainu_fs_mitoem2"
        if [[ -z "$PREV_UNET_PATH" ]]; then
            log "❌ ERROR: Previous UNet checkpoint not found for round $round"
            exit 1
        fi
        unet_cmd="python train/train_unet.py +task=$unet_config ++task.override_unet_config.trainer.checkpoint_dir=\"\${output_root}/fs_unet_ckpt/mitoem_${round}\" ++task.override_unet_config.trainer.pre_trained=\"$PREV_UNET_PATH\""
    fi
    
    run_command "$unet_cmd" "UNet Training Round $round"
    
    # Get current round UNet checkpoint path
    CURRENT_UNET_PATH=$(get_latest_unet_checkpoint "$round")
    if [[ -z "$CURRENT_UNET_PATH" ]]; then
        log "❌ ERROR: UNet checkpoint not found after training round $round"
        exit 1
    fi
    log "📁 UNet checkpoint for round $round: $CURRENT_UNET_PATH"
    
    # Step 2: UNet Inference
    log ""
    log "🔍 Step 2: UNet Inference (Round $round)"
    
    unet_inference_cmd="python predict/predict_new.py +task=in_mitoem_u ++task.override_unet_config.model_path=\"$CURRENT_UNET_PATH\""
    run_command "$unet_inference_cmd" "UNet Inference Round $round"
    
    # Step 3: UNet Evaluation
    log ""
    log "📊 Step 3: UNet Evaluation (Round $round)"
    
    unet_eval_cmd="python predict/evaluate.py --config-name evaluate_mitoem_u ++custom_suffix=\"${round}_unet\""
    run_command "$unet_eval_cmd" "UNet Evaluation Round $round"
    
    # Step 4: SAM2 Training
    log ""
    log "🎯 Step 4: SAM2 Training (Round $round)"
    
    if [[ $round -eq 1 ]]; then
        # First round: use default config
        sam2_cmd="python train/train.py +task=train_sam_mitoem ++task.config_overrides.launcher.experiment_log_dir=\"\${hydra:runtime.cwd}/output/finetune_sam_ckpt/mitoem_${round}\""
    else
        # Subsequent rounds: use previous SAM2 checkpoint
        if [[ -z "$PREV_SAM2_EXP" ]]; then
            log "❌ ERROR: Previous SAM2 experiment not found for round $round"
            exit 1
        fi
        sam2_checkpoint_path="$PREV_SAM2_EXP/checkpoints/checkpoint.pt"
        sam2_cmd="python train/train.py +task=train_sam_mitoem ++task.config_overrides.launcher.ckpt_path=\"$sam2_checkpoint_path\" ++task.config_overrides.launcher.experiment_log_dir=\"\${hydra:runtime.cwd}/output/finetune_sam_ckpt/mitoem_${round}\""
    fi
    
    run_command "$sam2_cmd" "SAM2 Training Round $round"
    
    # Get current round SAM2 experiment path
    CURRENT_SAM2_EXP=$(get_latest_sam2_experiment)
    if [[ -z "$CURRENT_SAM2_EXP" ]]; then
        log "❌ ERROR: SAM2 experiment not found after training round $round"
        exit 1
    fi
    log "📁 SAM2 experiment for round $round: $CURRENT_SAM2_EXP"
    
    # Step 5: SAM2 Inference
    log ""
    log "🔍 Step 5: SAM2 Inference (Round $round)"
    
    sam2_model_path="$CURRENT_SAM2_EXP/checkpoints/checkpoint.pt"
    sam2_inference_cmd="python predict/predict_new.py +task=in_mitoem ++task.sam2_model_path=\"$sam2_model_path\" ++task.override_unet_config.model_path=\"$CURRENT_UNET_PATH\""
    run_command "$sam2_inference_cmd" "SAM2 Inference Round $round"
    
    # Step 6: SAM2 Evaluation
    log ""
    log "📊 Step 6: SAM2 Evaluation (Round $round)"
    
    sam2_eval_cmd="python predict/evaluate.py --config-name evaluate_mitoem ++task.custom_suffix=\"${round}_sam\""
    run_command "$sam2_eval_cmd" "SAM2 Evaluation Round $round"
    
    # Update previous paths for next round
    PREV_UNET_PATH="$CURRENT_UNET_PATH"
    PREV_SAM2_EXP="$CURRENT_SAM2_EXP"
    
    log "✅ Round $round completed successfully"
done

log ""
log "🎉 All $NUM_ROUNDS rounds completed successfully!"
log "Finished at: $(date)"

# Summary
log ""
log "📋 Training Summary:"
log "  - Total rounds: $NUM_ROUNDS"
log "  - Final UNet checkpoint: $PREV_UNET_PATH"
log "  - Final SAM2 experiment: $PREV_SAM2_EXP"
log "  - Results can be found in the output directory"
